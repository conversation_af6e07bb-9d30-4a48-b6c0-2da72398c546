# Docker Lab

## Introductie

Het doel van deze labopdracht is om je inzicht te geven in Docker en je te leren hoe je containers, images, netwerken en volumes effectief kunt gebruiken. Je gaat docker gebruiken om zelf een kleine omgeving op te zetten, bestaand uit een webserver en een databaseserver.

Aan het einde van deze opdracht:

- Begrijp je het concept van containerisatie en waarom Docker wordt gebruikt.
- Kun je Docker containers starten, beheren en stoppen.
- Kun je Docker images bouwen en begrijpen hoe layers en caching werken.
- Kun je netwerken tussen containers opzetten en begrijpen hoe ze communiceren.
- Kun je volumes inzetten om data persistent te maken en het verschil met bind mounts uitleggen.

**Belangrijk:**

In eerdere opdrachten heb je vooral gewerkt met clickops (gebruik van een grafische interface). In deze opdracht leer je de command line gebruiken, omdat dit je meer controle geeft en essentieel is voor professioneel infrastructuurbeheer. Daarom gebruiken we NIET de Docker Desktop GUI, maar alleen de command line.

### Waarom de command line?

- In een productieomgeving heb je vaak geen grafische interface.
- Veel taken kunnen sneller en efficiënter via de command line.
- Je krijgt dieper inzicht in hoe Docker werkt.

Hoewel je Docker-commando's in cmd of PowerShell kunt uitvoeren, raden we aan om een Ubuntu shell binnen WSL2 te gebruiken voor een Linux-achtige ervaring. Open een Ubuntu-terminal door in Windows op Start > Ubuntu te klikken of via `wsl.exe` in PowerShell.

---

## Inleveren:

- Schrijf je verslag in Markdown en zet het in de labs-folder in je persoonlijke Learning Journey repository. Maak een PDF hiervan en lever dit in via de DLO.
- Vermeld alle bronnen die je hebt gebruikt bij je antwoorden als Markdown-footnotes.
- Gebruik de juiste Markdown-formattering voor code, lijsten en koppen.

Voorbeelden:

### Kopjes:

```markdown
## Dit is een kopje
Lijst:
markdown
Copy code
- Dit is een lijstitem
- Dit is een ander lijstitem
Codeblok:
bash
Copy code
docker run hello-world
Voetnoot voor bronvermelding:
markdown
Copy code
Dit is een statement met een bron[^1].

[^1]: Dit is de voetnoot met de referentie.
Schrijf je eigen antwoorden. Als je ChatGPT gebruikt, leer je er niets van. Zoek daarom zelf naar bronnen en verwerk de informatie in je eigen woorden.

Reflecteer op wat je hebt geleerd: Wat vond je het meest uitdagend en waarom?

1. Werken met Docker Containers
Docker is een populaire tool om applicaties in containers te draaien. Een container is een lichtgewicht, geïsoleerde omgeving waarin een applicatie en alle benodigde afhankelijkheden worden gebundeld. Containers maken het eenvoudig om applicaties overal op dezelfde manier uit te voeren, zonder afhankelijk te zijn van de onderliggende infrastructuur.

Bekijk de volgende pagina in de Docker documentatie:
https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-container/

Vraag: Wat is het verschil tussen een container en een Virtual Machine? Waar zou je een VM voor gebruiken, en waar een Docker container?

Taak 1.1: Een container starten en verkennen
Lees over docker run en docker ps in de Docker-documentatie:
https://docs.docker.com/engine/reference/commandline/container/

We starten een eenvoudige Ubuntu-container en verkennen hoe deze werkt.

Voer uit:

bash
Copy code
docker run -it ubuntu bash
Wat gebeurt er als je dit uitvoert? Wat valt je op?
Hoe weet je dat je nu in een container zit?

Voer in de container de volgende commando's uit:

bash
Copy code
ls
pwd
whoami
Wat zie je als output? Wat vertelt dit je over de containeromgeving?
Is dit anders dan op je eigen machine? Waarom?

Verlaat de container zonder deze te stoppen.

Hint: Zoek in docker run --help (in een andere terminal! (waarom?)) hoe je een container kunt loskoppelen.

Controleer de actieve containers:

bash
Copy code
docker ps
Wat zie je als output? Wat betekenen de velden?

Sluit de container af en bekijk alle containers, inclusief gestopte.

Welk commando heb je gebruikt? Wat was de output? Waar kon je die informatie vinden?

Waarom zou je een container stoppen in plaats van verwijderen?

Verwijder nu de container, en controleer of hij ook echt weg is.

Welk commando heb je gebruikt? Wat was de output? Waar kon je die informatie vinden?

2. Docker Images
Docker images vormen de bouwstenen van containers. Ze worden gedefinieerd in een Dockerfile en bestaan uit meerdere lagen.

Lees over docker build en Dockerfile instructies:
https://docs.docker.com/engine/reference/commandline/build/

Taak 2.1: Images
Download een specifieke image:

bash
Copy code
docker pull python:3.11-alpine
Leg uit wat er gebeurt.

Wat betekent een image bestaat uit meerdere lagen, zie je dat terug in de output?

Waar worden deze images opgeslagen? Hoe kun je een lijst van alle gedownloade images opvragen?

Wie maakt de images op de Docker hub, kun je die images altijd vertrouwen? Op basis waarvan? Wat zijn de risico's? Bronnen?

Taak 2.2: De Flask-app maken
Maak een map flask-app en voeg toe:

app.py

python
Copy code
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, World!"
requirements.txt

nginx
Copy code
flask
mysql-connector-python
Taak 2.3: Dockerfile maken
Maak een Dockerfile in flask-app:

dockerfile
Copy code
FROM python:3.11-alpine
WORKDIR /app
COPY . /app
RUN pip install -r requirements.txt
CMD FLASK_ENV=development python -m flask --app flask-app run --host=0.0.0.0 --port=80 --debug
Bouw en start de container:

bash
Copy code
docker build -t flask-app .
docker run -d -p 5000:80 flask-app
Waarom gebruiken we een aparte requirements.txt in plaats van direct in de Dockerfile de pakketten te installeren?

Wat gebeurt er in de verschillende stappen van de Dockerfile?

Waarom gebruiken we COPY om de broncode in de container te zetten?

Wat als we onze code vaak willen wijzigen zonder steeds de image opnieuw te bouwen, kan dat?

3. Docker Networks - Containers laten communiceren
Hoe kunnen twee containers met elkaar communiceren?

Containers kunnen via Docker-netwerken met elkaar verbonden worden. Standaard gebruikt Docker een bridge-netwerk, maar je kunt ook je eigen netwerken maken.

Lees over docker network create en hoe containers elkaar vinden:
https://docs.docker.com/network/

Taak 3.1: Netwerken maken en gebruiken
Maak een Docker netwerk my_network via de CLI
Welk commando heb je gebruikt; controleer via de CLI welke netwerken je hebt in Docker.

Start een MySQL-container die met dit netwerk is verbonden. We gebruiken hiervoor de standaard docker image van MySQL:

bash
Copy code
docker run -d --name db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword mysql:8.0
Leg in je eigen woorden uit wat er gebeurt.

Wat is MYSQL_ROOT_PASSWORD voor iets (Hint: Wat is -e in docker)? Kun je dat bij elke image gebruiken?

Zijn er bij dit image nog andere dingen die je kunt invullen na -e?

Wat betekenen alle andere opties in het commando hierboven?

Start een Ubuntu-container in hetzelfde netwerk:

bash
Copy code
docker run -it --rm --network my_network ubuntu bash
Waarom is het belangrijk dat de client en de database in hetzelfde netwerk zitten?

Installeer MySQL-client binnen de container:

bash
Copy code
apt update && apt install -y mysql-client
Wat is apt en wat gebeurt hier?

Verbind met de MySQL-container:

bash
Copy code
mysql -h db -u root -p
Waarom gebruik je de naam ‘db’ in plaats van een IP-adres? Wat is het voordeel hiervan?

4. Docker Volumes - Data Persistent maken
Vraag: Wat gebeurt er met data in een container als deze wordt gestopt of verwijderd?

Standaard verdwijnt data als je een container verwijdert. Met volumes kun je ervoor zorgen dat data bewaard blijft.

Lees over bind mounts en volumes:
https://docs.docker.com/storage/

Taak 4: Data behouden met volumes
Bind mount voor de Flask-app:

bash
Copy code
docker run -d -p 5000:80 -v "$(pwd)"/flask-app:/app flask-app
Wat verandert er nu? Wat gebeurt er als je app.py aanpast?

Hoe werkt dit anders dan COPY in de Dockerfile?

Volume gebruiken voor de database:

bash
Copy code
docker volume create mysqldata
docker run -d --name mysql-db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword -v mysqldata:/var/lib/mysql mysql:8.0
Waarom gebruiken we een volume voor MySQL en geen bind mount?

5. Docker Compose
Hoe kun je meerdere containers eenvoudig beheren zonder handmatig meerdere docker run-commando’s uit te voeren?

Docker Compose stelt je in staat om meerdere containers in één configuratiebestand te definiëren en in één keer te starten.

Lees over compose.yml en service-definities:
https://docs.docker.com/compose/

Taak 5.1: compose.yml maken
yaml
Copy code
version: '3'
services:
  web:
    build: ./flask-app
    ports:
      - "5000:80"
    volumes:
      - ./flask-app:/app
    depends_on:
      - db
    networks:
      - my_network

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: mysecretpassword
    volumes:
      - mysqldata:/var/lib/mysql
    networks:
      - my_network

networks:
  my_network:

volumes:
  mysqldata:
Run:

bash
Copy code
docker compose up -d
Hoe kun je controleren of de database goed draait?

Je ziet ook wel eens docker-compose als commando staan, wat is het verschil? Waarom gebruiken wij docker compose?

Leg uit wat er allemaal gebeurt in de docker compose; zie je dat terug in de output?

Taak 5.2: netwerkdiagram maken
Het is vaak handig om een diagram te hebben om je infrastructuur te beschrijven. Dit kan met veel verschillende tools. Vandaag gaan we draw.io gebruiken.

Installeer de draw.io (unofficial) extensie voor Visual Studio Code

Maak een nieuwe file aan network.drawio.png.

Open dit bestand in VSCode en gebruik de editor om een diagram te maken waarin je jouw docker netwerk uittekent.

Zorg dat je de containers, de host en de services laat zien.

Vergeet niet hostnames, ports en andere relevante informatie toe te voegen.

Voeg de png toe aan je verslag:

markdown
Copy code
![Netwerkdiagram](network.drawio.png)
Voeg een korte uitleg toe van je diagram en hoe services met elkaar communiceren.

Beschrijf in je eigen woorden: Hoe ondersteunt dit netwerkdiagram jouw begrip van de Docker Compose configuratie en wat zijn volgens jou de voordelen van het visueel in kaart brengen van deze infrastructuur?

yaml
Copy code

---

Wil je dat ik dit ook direct voor je **exporteer als `.md` bestand**, zodat je het meteen kunt in je repo zetten?






You said:
dat is niet de hele opdracht in markdown, je hoeft het niet te maken



2/2

ChatGPT said:

Thought for 3m 11s
markdown
Copy code
# Docker Lab

## Introductie

Het doel van deze labopdracht is om je inzicht te geven in Docker en je te leren hoe je containers, images, netwerken en volumes effectief kunt gebruiken. Je gaat docker gebruiken om zelf een kleine omgeving op te zetten, bestaand uit een webserver en een databaseserver.

Aan het einde van deze opdracht:

- Begrijp je het concept van containerisatie en waarom Docker wordt gebruikt.
- Kun je Docker containers starten, beheren en stoppen.
- Kun je Docker images bouwen en begrijpen hoe layers en caching werken.
- Kun je netwerken tussen containers opzetten en begrijpen hoe ze communiceren.
- Kun je volumes inzetten om data persistent te maken en het verschil met bind mounts uitleggen.

**Belangrijk:**

In eerdere opdrachten heb je vooral gewerkt met clickops (gebruik van een grafische interface). In deze opdracht leer je de command line gebruiken, omdat dit je meer controle geeft en essentieel is voor professioneel infrastructuurbeheer. Daarom gebruiken we **NIET** de Docker Desktop GUI, maar alleen de command line.

### Waarom de command line?

- In een productieomgeving heb je vaak geen grafische interface.
- Veel taken kunnen sneller en efficiënter via de command line.
- Je krijgt dieper inzicht in hoe Docker werkt.

Hoewel je Docker-commando's in cmd of PowerShell kunt uitvoeren, raden we aan om een Ubuntu shell binnen WSL2 te gebruiken voor een Linux-achtige ervaring. Open een Ubuntu-terminal door in Windows op **Start > Ubuntu** te klikken of via `wsl.exe` in PowerShell.

---

## Inleveren:

- Schrijf je verslag in **Markdown** en zet het in de `labs`-folder in je persoonlijke Learning Journey repository. Maak een **PDF** hiervan en lever dit in via de DLO.
- Vermeld alle bronnen die je hebt gebruikt bij je antwoorden als **Markdown-footnotes**.
- Gebruik de juiste Markdown-formattering voor code, lijsten en koppen.

**Voorbeelden:**

**Kopjes:**
```markdown
## Dit is een kopje
Lijst:

markdown
Copy code
- Dit is een lijstitem
- Dit is een ander lijstitem
Codeblok:

bash
Copy code
docker run hello-world
Voetnoot voor bronvermelding:

markdown
Copy code
Dit is een statement met een bron[^1].

[^1]: Dit is de voetnoot met de referentie.
Schrijf je eigen antwoorden. Als je ChatGPT gebruikt, leer je er niets van. Zoek daarom zelf naar bronnen en verwerk de informatie in je eigen woorden.

Reflecteer op wat je hebt geleerd: Wat vond je het meest uitdagend en waarom?

1. Werken met Docker Containers
Docker is een populaire tool om applicaties in containers te draaien. Een container is een lichtgewicht, geïsoleerde omgeving waarin een applicatie en alle benodigde afhankelijkheden worden gebundeld. Containers maken het eenvoudig om applicaties overal op dezelfde manier uit te voeren, zonder afhankelijk te zijn van de onderliggende infrastructuur.

Bekijk de volgende pagina in de Docker documentatie:
https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-container/

Vraag: Wat is het verschil tussen een container en een Virtual Machine? Waar zou je een VM voor gebruiken, en waar een Docker container?

Taak 1.1: Een container starten en verkennen
Lees over docker run en docker ps in de Docker-documentatie:
https://docs.docker.com/engine/reference/commandline/container/

We starten een eenvoudige Ubuntu-container en verkennen hoe deze werkt.

Voer uit:

bash
Copy code
docker run -it ubuntu bash
Wat gebeurt er als je dit uitvoert? Wat valt je op?

Hoe weet je dat je nu in een container zit?

Voer in de container de volgende commando's uit:

bash
Copy code
ls
pwd
whoami
Wat zie je als output? Wat vertelt dit je over de containeromgeving?

Is dit anders dan op je eigen machine? Waarom?

Verlaat de container zonder deze te stoppen.

Hint: Zoek in docker run --help (in een andere terminal! (waarom?)) hoe je een container kunt loskoppelen.

Controleer de actieve containers:

bash
Copy code
docker ps
Wat zie je als output? Wat betekenen de velden?

Sluit de container af en bekijk alle containers, inclusief gestopte.

Welk commando heb je gebruikt? Wat was de output? Waar kon je die informatie vinden?

Waarom zou je een container stoppen in plaats van verwijderen?

Verwijder nu de container, en controleer of hij ook echt weg is.

Welk commando heb je gebruikt? Wat was de output? Waar kon je die informatie vinden?

2. Docker Images
Docker images vormen de bouwstenen van containers. Ze worden gedefinieerd in een Dockerfile en bestaan uit meerdere lagen.

Lees over docker build en Dockerfile instructies:
https://docs.docker.com/engine/reference/commandline/build/

Taak 2.1: Images
Download een specifieke image:

bash
Copy code
docker pull python:3.11-alpine
Leg uit wat er gebeurt.

Wat betekent dat een image uit meerdere lagen bestaat; zie je dat terug in de output?

Waar worden deze images opgeslagen? Hoe kun je een lijst van alle gedownloade images opvragen?

Wie maakt de images op de Docker Hub; kun je die images altijd vertrouwen? Op basis waarvan? Wat zijn de risico's? (Bronnen?)

Taak 2.2: De Flask-app maken
We bouwen een eenvoudige webserver met Flask.

Maak een map flask-app en voeg toe:

app.py

python
Copy code
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, World!"
requirements.txt

nginx
Copy code
flask
mysql-connector-python
Taak 2.3: Dockerfile maken
Maak een Dockerfile in flask-app:

dockerfile
Copy code
FROM python:3.11-alpine
WORKDIR /app
COPY . /app
RUN pip install -r requirements.txt
CMD FLASK_ENV=development python -m flask --app flask-app run --host=0.0.0.0 --port=80 --debug
Bouw en start de container:

bash
Copy code
docker build -t flask-app .
docker run -d -p 5000:80 flask-app
Waarom gebruiken we een aparte requirements.txt in plaats van direct in de Dockerfile de pakketten te installeren?

Wat gebeurt er in de verschillende stappen van de Dockerfile?

Waarom gebruiken we COPY om de broncode in de container te zetten?

Wat als we onze code vaak willen wijzigen zonder steeds de image opnieuw te bouwen, kan dat?
Opmerking: Later gebruiken we bind mounts om dit te verbeteren.

3. Docker Networks - Containers laten communiceren
Hoe kunnen twee containers met elkaar communiceren?

Containers kunnen via Docker-netwerken met elkaar verbonden worden. Standaard gebruikt Docker een bridge-netwerk, maar je kunt ook je eigen netwerken maken.

Lees over docker network create en hoe containers elkaar vinden:
https://docs.docker.com/network/

Taak 3.1: Netwerken maken en gebruiken
Maak een Docker netwerk my_network via de CLI.

Welk commando heb je gebruikt; controleer via de CLI welke netwerken je hebt in Docker.

Start een MySQL-container die met dit netwerk is verbonden. We gebruiken hiervoor de standaard docker image van MySQL:

bash
Copy code
docker run -d --name db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword mysql:8.0
Leg in je eigen woorden uit wat er gebeurt.

Wat is MYSQL_ROOT_PASSWORD voor iets (Hint: wat is -e in docker)? Kun je dat bij elke image gebruiken?

Zijn er bij dit image nog andere dingen die je kunt invullen na -e?

Wat betekenen alle andere opties in het commando hierboven?

Start een Ubuntu-container in hetzelfde netwerk:

bash
Copy code
docker run -it --rm --network my_network ubuntu bash
Waarom is het belangrijk dat de client en de database in hetzelfde netwerk zitten?

Installeer MySQL-client binnen de container:

bash
Copy code
apt update && apt install -y mysql-client
Wat is apt en wat gebeurt hier?

Verbind met de MySQL-container:

bash
Copy code
mysql -h db -u root -p
Waarom gebruik je de naam db in plaats van een IP-adres? Wat is het voordeel hiervan?

4. Docker Volumes - Data Persistent maken
Vraag: Wat gebeurt er met data in een container als deze wordt gestopt of verwijderd?

Standaard verdwijnt data als je een container verwijdert. Met volumes kun je ervoor zorgen dat data bewaard blijft.

Lees over bind mounts en volumes:
Docker Storage (https://docs.docker.com/storage/)

Taak 4: Data behouden met volumes
Bind mount voor de Flask-app

bash
Copy code
docker run -d -p 5000:80 -v "$(pwd)"/flask-app:/app flask-app
Wat verandert er nu? Wat gebeurt er als je app.py aanpast?

Hoe werkt dit anders dan COPY in de Dockerfile?

Volume gebruiken voor de database

bash
Copy code
docker volume create mysqldata
docker run -d --name mysql-db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword -v mysqldata:/var/lib/mysql mysql:8.0
Waarom gebruiken we een volume voor MySQL en geen bind mount?

5. Docker Compose
Hoe kun je meerdere containers eenvoudig beheren zonder handmatig meerdere docker run-commando’s uit te voeren?

Docker Compose stelt je in staat om meerdere containers in één configuratiebestand te definiëren en in één keer te starten.

Lees over compose.yml en service-definities:
https://docs.docker.com/compose/

Taak 5.1: compose.yml maken
yaml
Copy code
version: '3'
services:
  web:
    build: ./flask-app
    ports:
      - "5000:80"
    volumes:
      - ./flask-app:/app
    depends_on:
      - db
    networks:
      - my_network

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: mysecretpassword
    volumes:
      - mysqldata:/var/lib/mysql
    networks:
      - my_network

networks:
  my_network:

volumes:
  mysqldata:
Start de stack:

bash
Copy code
docker compose up -d
Hoe kun je controleren of de database goed draait?

Je ziet ook wel eens docker-compose als commando staan, wat is het verschil? Waarom gebruiken wij docker compose?

Leg uit wat er allemaal gebeurt in de docker compose; zie je dat terug in de output?

Taak 5.2: netwerkdiagram maken
Het is vaak handig om een diagram te hebben om je infrastructuur te beschrijven. Dit kan met veel verschillende tools. Vandaag gaan we draw.io gebruiken.

Installeer de draw.io (unofficial) extensie voor Visual Studio Code.

Maak een nieuw bestand network.drawio.png. De extensie .drawio.png zorgt ervoor dat je het bestand binnen VSCode kunt openen met de draw.io editor, maar dat je het bestand ook direct kunt benaderen als PNG (plaatje).

Open network.drawio.png in VSCode en gebruik de editor om een diagram te maken waarin je jouw docker netwerk uittekent. Zorg dat je de containers, de host en de services laat zien. Vergeet niet hostnames, ports, en andere relevante informatie toe te voegen.

Voeg de PNG toe aan je verslag:

markdown
Copy code
![Netwerkdiagram](network.drawio.png)
Voeg een korte uitleg toe van je diagram en hoe services met elkaar communiceren.

Beschrijf in je eigen woorden: Hoe ondersteunt dit netwerkdiagram jouw begrip van de Docker Compose configuratie en wat zijn volgens jou de voordelen van het visueel in kaart brengen van deze infrastructuur?