# Docker Network Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                        HOST MACHINE                         │
│                     (Windows/Linux)                         │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                Docker Engine                        │    │
│  │                                                     │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │            my_network (bridge)              │    │    │
│  │  │                                             │    │    │
│  │  │  ┌─────────────────┐  ┌─────────────────┐   │    │    │
│  │  │  │   web container │  │   db container  │   │    │    │
│  │  │  │   (Flask App)   │  │    (MySQL)      │   │    │    │
│  │  │  │                 │  │                 │   │    │    │
│  │  │  │ hostname: web   │  │ hostname: db    │   │    │    │
│  │  │  │ internal: :80   │  │ internal: :3306 │   │    │    │
│  │  │  │                 │  │                 │   │    │    │
│  │  │  └─────────────────┘  └─────────────────┘   │    │    │
│  │  │           │                      │          │    │    │
│  │  └───────────┼──────────────────────┼──────────┘    │    │
│  │              │                      │               │    │
│  └──────────────┼──────────────────────┼───────────────┘    │
│                 │                      │                    │
│  ┌──────────────┼──────────────────────┼───────────────┐    │
│  │              │                      │               │    │
│  │  Port        │                      │               │    │
│  │  Mapping     │                      │               │    │
│  │              │                      │               │    │
│  │  5000:80 ────┘                      │               │    │
│  │                                     │               │    │
│  │  Volumes:                           │               │    │
│  │  ./flask-app:/app (bind mount)      │               │    │
│  │                                     │               │    │
│  │  mysqldata:/var/lib/mysql ──────────┘               │    │
│  │  (named volume)                                     │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTP Request
                              │ localhost:5000
                              │
                    ┌─────────────────┐
                    │     Browser     │
                    │   (External)    │
                    └─────────────────┘

Communication Flow:
1. Browser → Host:5000 → web container:80
2. web container → db container:3306 (via hostname 'db')
3. Data persistence via volumes (survives container restarts)
```

## Network Components:

- **Host Machine**: Physical/virtual machine running Docker
- **Docker Engine**: Container runtime managing all containers
- **my_network**: Custom bridge network for container communication
- **web container**: Flask application accessible from outside
- **db container**: MySQL database only accessible within network
- **Port Mapping**: 5000:80 allows external access to web app
- **Bind Mount**: Live code editing without rebuilding image
- **Named Volume**: Persistent database storage
