# Docker Lab - Uitwerking

Dit is de complete uitwerking van de Docker Lab opdracht voor ICT Infrastructure.

## Bestanden

- `docker-lab-verslag.md` - Hoofdverslag met alle antwoorden en uitwerkingen
- `flask-app/` - Directory met Flask applicatie
  - `app.py` - Python Flask webserver
  - `requirements.txt` - Python dependencies
  - `Dockerfile` - Docker image definitie
- `compose.yml` - Docker Compose configuratie
- `network-diagram.md` - Netwerkdiagram documentatie

## Hoe te gebruiken

### 1. Individuele containers

**Flask app bouwen en draaien:**
```bash
cd flask-app
docker build -t flask-app .
docker run -d -p 5000:80 flask-app
```

**MySQL database starten:**
```bash
docker network create my_network
docker run -d --name db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword mysql:8.0
```

### 2. Met <PERSON>er Compose (aanbevolen)

**Hele stack starten:**
```bash
docker compose up -d
```

**Status controleren:**
```bash
docker compose ps
docker compose logs
```

**Stoppen:**
```bash
docker compose down
```

**Met volumes verwijderen:**
```bash
docker compose down -v
```

## Toegang

- **Web applicatie:** http://localhost:5000
- **MySQL database:** Alleen toegankelijk binnen Docker netwerk via hostname `db`

## Vereisten

- Docker Desktop of Docker Engine geïnstalleerd
- Docker Compose (inbegrepen in Docker Desktop)

## Opdracht details

Het verslag behandelt alle onderdelen van de Docker Lab:

1. **Docker Containers** - Basis container operaties
2. **Docker Images** - Image management en Dockerfile
3. **Docker Networks** - Container communicatie
4. **Docker Volumes** - Data persistentie
5. **Docker Compose** - Multi-container orchestratie

Zie `docker-lab-verslag.md` voor de complete uitwerking met antwoorden op alle vragen.
