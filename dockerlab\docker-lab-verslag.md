# Docker Lab Verslag

**Naam:** [Je naam]  
**Datum:** 16 september 2025  
**Vak:** ICT Infrastructure  

## Inleiding

In dit lab heb ik gewerkt met Docker om containers, images, netwerken en volumes te leren gebruiken. Het doel was om een webserver (Flask) en databaseserver (MySQL) op te zetten en deze met elkaar te laten communiceren via Docker.

## 1. Werken met Docker Containers

### Vraag: Wat is het verschil tussen een container en een Virtual Machine?

**Container vs Virtual Machine:**

Een **container** is een lichtgewicht, geïsoleerde omgeving die een applicatie en alle benodigde afhankelijkheden bundelt. Containers delen de kernel van het host-besturingssysteem en gebruiken minder resources.

Een **Virtual Machine (VM)** is een complete virtualisatie van een computersysteem, inclusief een eigen besturingssysteem. VMs hebben meer overhead omdat ze een volledig OS draaien.

**Gebruik:**
- **VM gebruiken voor:** Verschillende besturingssystemen draaien, volledige isolatie vereist, legacy applicaties
- **Container gebruiken voor:** Microservices, CI/CD pipelines, snelle deployment, resource-efficiëntie[^1]

### Taak 1.1: Een container starten en verkennen

**Commando uitgevoerd:**
```bash
docker run -it ubuntu bash
```

**Wat gebeurt er:**
- Docker downloadt de Ubuntu image (als deze nog niet lokaal aanwezig is)
- Er wordt een nieuwe container gestart gebaseerd op de Ubuntu image
- De `-it` flags zorgen voor een interactieve terminal
- `bash` wordt uitgevoerd als het hoofdproces in de container

**Hoe weet je dat je in een container zit:**
- De hostname in de prompt is een willekeurige string (bijv. `root@a1b2c3d4e5f6`)
- Het bestandssysteem is minimaal en bevat alleen Ubuntu-bestanden
- De process ID's beginnen opnieuw vanaf 1

**Commando's in container:**
```bash
ls          # Toont Ubuntu directory structuur
pwd         # Output: /
whoami      # Output: root
```

**Observaties:**
- Het bestandssysteem is anders dan mijn host machine
- Ik ben automatisch root gebruiker
- Alleen essentiële Ubuntu bestanden zijn aanwezig

**Container loskoppelen:**
Gebruik `Ctrl+P` gevolgd door `Ctrl+Q` om de container te detachen zonder te stoppen.

**Actieve containers bekijken:**
```bash
docker ps
```

**Output betekenis:**
- CONTAINER ID: Unieke identifier
- IMAGE: Gebruikte image
- COMMAND: Uitgevoerd commando
- CREATED: Wanneer gemaakt
- STATUS: Huidige status
- PORTS: Poort mappings
- NAMES: Container naam

**Container stoppen en alle containers bekijken:**
```bash
docker stop [CONTAINER_ID]
docker ps -a
```

**Waarom stoppen vs verwijderen:**
Stoppen behoudt de container en eventuele wijzigingen. Verwijderen wist alles permanent. Stoppen is nuttig voor tijdelijke pauze of debugging.

**Container verwijderen:**
```bash
docker rm [CONTAINER_ID]
docker ps -a  # Verificatie dat container weg is
```

## 2. Docker Images

### Taak 2.1: Images

**Image downloaden:**
```bash
docker pull python:3.11-alpine
```

**Wat gebeurt er:**
Docker downloadt de Python 3.11 Alpine image van Docker Hub. Alpine is een minimale Linux distributie, waardoor de image kleiner is.

**Meerdere lagen:**
Ja, in de output zie je verschillende lagen worden gedownload:
```
Pulling from library/python
abc123: Pull complete
def456: Pull complete
...
```
Elke regel representeert een layer. Dit maakt caching en hergebruik efficiënter.

**Images opslag en lijst:**
- **Opslag:** Lokaal in Docker's storage directory
- **Lijst opvragen:** `docker images` of `docker image ls`

**Docker Hub betrouwbaarheid:**
Images op Docker Hub worden gemaakt door verschillende partijen:
- **Officiële images:** Onderhouden door Docker/softwareleveranciers (betrouwbaar)
- **Community images:** Gemaakt door gebruikers (variabele kwaliteit)

**Risico's:**
- Malware of backdoors
- Verouderde software met kwetsbaarheden
- Slechte configuratie

**Verificatie:**
- Gebruik officiële images waar mogelijk
- Check download statistieken en reviews
- Scan images op kwetsbaarheden[^2]

### Taak 2.2: De Flask-app maken

**Directory structuur aanmaken:**
```bash
mkdir flask-app
cd flask-app
```

**app.py bestand:**
```python
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello, World!"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=80, debug=True)
```

**requirements.txt bestand:**
```
flask
mysql-connector-python
```

### Taak 2.3: Dockerfile maken

**Dockerfile:**
```dockerfile
FROM python:3.11-alpine
WORKDIR /app
COPY . /app
RUN pip install -r requirements.txt
CMD ["python", "-m", "flask", "--app", "app", "run", "--host=0.0.0.0", "--port=80", "--debug"]
```

**Image bouwen en container starten:**
```bash
docker build -t flask-app .
docker run -d -p 5000:80 flask-app
```

**Vragen beantwoord:**

**Waarom aparte requirements.txt?**
- **Caching:** Docker kan de pip install stap cachen als requirements.txt niet wijzigt
- **Herbruikbaarheid:** Andere ontwikkelaars kunnen dezelfde dependencies installeren
- **Duidelijkheid:** Expliciete lijst van afhankelijkheden

**Dockerfile stappen uitleg:**
1. `FROM python:3.11-alpine` - Basis image met Python 3.11 op Alpine Linux
2. `WORKDIR /app` - Stel werkdirectory in op /app
3. `COPY . /app` - Kopieer alle bestanden naar /app in container
4. `RUN pip install -r requirements.txt` - Installeer Python packages
5. `CMD [...]` - Standaard commando om Flask app te starten

**Waarom COPY gebruiken:**
COPY brengt de broncode in de image. Dit zorgt ervoor dat de applicatie beschikbaar is binnen de container, onafhankelijk van de host.

**Code wijzigen zonder rebuild:**
Ja, dit kan met **bind mounts**. In plaats van COPY gebruik je een volume mount om de lokale directory te koppelen aan de container.

## 3. Docker Networks - Containers laten communiceren

### Taak 3.1: Netwerken maken en gebruiken

**Docker netwerk aanmaken:**
```bash
docker network create my_network
```

**Netwerken controleren:**
```bash
docker network ls
```

**MySQL container starten:**
```bash
docker run -d --name db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword mysql:8.0
```

**Uitleg commando:**
- `docker run` - Start nieuwe container
- `-d` - Detached mode (op achtergrond)
- `--name db` - Geef container naam "db"
- `--network my_network` - Verbind met custom netwerk
- `-e MYSQL_ROOT_PASSWORD=mysecretpassword` - Stel environment variabele in
- `mysql:8.0` - Gebruik MySQL 8.0 image

**MYSQL_ROOT_PASSWORD uitleg:**
- `-e` stelt environment variabelen in
- MySQL image vereist dit voor root wachtwoord
- Niet elke image gebruikt environment variabelen
- Check image documentatie voor beschikbare variabelen

**Andere MySQL environment variabelen:**
- `MYSQL_DATABASE` - Maak database aan bij start
- `MYSQL_USER` - Maak gebruiker aan
- `MYSQL_PASSWORD` - Wachtwoord voor gebruiker

**Ubuntu container in zelfde netwerk:**
```bash
docker run -it --rm --network my_network ubuntu bash
```

**Waarom zelfde netwerk belangrijk:**
Containers in hetzelfde netwerk kunnen elkaar vinden via hostname. Zonder custom netwerk kunnen containers niet direct communiceren.

**MySQL client installeren:**
```bash
apt update && apt install -y mysql-client
```

**Wat is apt:**
APT (Advanced Package Tool) is de package manager voor Debian/Ubuntu. Het downloadt en installeert software packages.

**Verbinden met MySQL:**
```bash
mysql -h db -u root -p
```

**Waarom hostname 'db':**
Docker's ingebouwde DNS resolver vertaalt container namen naar IP-adressen binnen hetzelfde netwerk. Dit is flexibeler dan hardcoded IP-adressen.

## 4. Docker Volumes - Data Persistent maken

### Vraag: Wat gebeurt er met data als container wordt gestopt/verwijderd?

Standaard verdwijnt alle data in een container wanneer deze wordt verwijderd. De container filesystem is **ephemeral** (tijdelijk).

### Taak 4: Data behouden met volumes

**Bind mount voor Flask-app:**
```bash
docker run -d -p 5000:80 -v "$(pwd)"/flask-app:/app flask-app
```

**Wat verandert er:**
- Lokale `flask-app` directory wordt gekoppeld aan `/app` in container
- Wijzigingen in `app.py` zijn direct zichtbaar zonder rebuild
- Hot reloading werkt omdat Flask debug mode staat aan

**Verschil met COPY:**
- **COPY:** Kopieert bestanden naar image tijdens build (statisch)
- **Bind mount:** Koppelt host directory aan container (dynamisch)

**Volume voor database:**
```bash
docker volume create mysqldata
docker run -d --name mysql-db --network my_network -e MYSQL_ROOT_PASSWORD=mysecretpassword -v mysqldata:/var/lib/mysql mysql:8.0
```

**Waarom volume voor MySQL:**
- **Persistentie:** Database data blijft bestaan na container restart
- **Performance:** Docker volumes zijn geoptimaliseerd voor I/O
- **Portabiliteit:** Volume kan naar andere containers worden verplaatst
- **Beveiliging:** Host heeft geen directe toegang tot database bestanden

## 5. Docker Compose

### Taak 5.1: compose.yml maken

**compose.yml bestand:**
```yaml
version: '3'
services:
  web:
    build: ./flask-app
    ports:
      - "5000:80"
    volumes:
      - ./flask-app:/app
    depends_on:
      - db
    networks:
      - my_network

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: mysecretpassword
    volumes:
      - mysqldata:/var/lib/mysql
    networks:
      - my_network

networks:
  my_network:

volumes:
  mysqldata:
```

**Stack starten:**
```bash
docker compose up -d
```

**Database status controleren:**
```bash
docker compose ps
docker compose logs db
```

**docker-compose vs docker compose:**
- `docker-compose` - Standalone tool (oudere versie)
- `docker compose` - Geïntegreerd in Docker CLI (nieuwere versie)
- Wij gebruiken `docker compose` omdat het de moderne, ondersteunde versie is

**Wat gebeurt er bij docker compose up:**
1. Netwerk `my_network` wordt aangemaakt
2. Volume `mysqldata` wordt aangemaakt
3. Database container `db` wordt gestart
4. Flask app wordt gebouwd (als image niet bestaat)
5. Web container `web` wordt gestart
6. Containers worden verbonden via netwerk

### Taak 5.2: Netwerkdiagram maken

Voor het netwerkdiagram heb ik een tekstuele representatie gemaakt (zie network-diagram.md voor details):

```
┌─────────────────────────────────────────────────────────────┐
│                        HOST MACHINE                         │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                Docker Engine                        │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │            my_network (bridge)              │    │    │
│  │  │  ┌─────────────────┐  ┌─────────────────┐   │    │    │
│  │  │  │   web container │  │   db container  │   │    │    │
│  │  │  │   (Flask App)   │  │    (MySQL)      │   │    │    │
│  │  │  │ hostname: web   │  │ hostname: db    │   │    │    │
│  │  │  │ port: 80        │  │ port: 3306      │   │    │    │
│  │  │  └─────────────────┘  └─────────────────┘   │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  └─────────────────────────────────────────────────────┘    │
│                         │                                   │
│  Port Mapping: 5000:80 ─┘                                   │
└─────────────────────────────────────────────────────────────┘
                          │
                ┌─────────────────┐
                │     Browser     │
                │ localhost:5000  │
                └─────────────────┘
```

**Diagram uitleg:**
Het diagram toont:
- **Host machine** met Docker Engine
- **my_network** bridge netwerk
- **Web container** (Flask app) op poort 80, gemapped naar host poort 5000
- **DB container** (MySQL) op poort 3306
- **Volume mount** tussen host directory en web container
- **Persistent volume** voor database data

**Service communicatie:**
- Web container communiceert met database via hostname `db`
- Externe toegang tot web app via `localhost:5000`
- Database is alleen toegankelijk binnen het Docker netwerk
- Data persisteert via volumes ook na container restart

**Voordelen van visueel diagram:**
- **Overzicht:** Snel begrip van architectuur
- **Documentatie:** Helpt bij onderhoud en troubleshooting
- **Communicatie:** Makkelijk uit te leggen aan teamleden
- **Planning:** Identificeert potentiële bottlenecks of security issues

## Reflectie

**Meest uitdagend:**
Het begrijpen van Docker netwerken was het meest uitdagend. Het concept dat containers elkaar kunnen vinden via hostnamen binnen hetzelfde netwerk was nieuw voor mij. Ook het verschil tussen bind mounts en volumes kostte tijd om goed te begrijpen.

**Geleerd:**
- Docker containers zijn veel lichter dan VMs
- Layered images maken caching en hergebruik efficiënt
- Netwerken zijn essentieel voor multi-container applicaties
- Volumes zijn cruciaal voor data persistentie
- Docker Compose vereenvoudigt multi-container orchestratie

**Praktische waarde:**
Deze kennis is direct toepasbaar voor:
- Development environments opzetten
- Microservices architectuur
- CI/CD pipelines
- Production deployments

## Bronnen

[^1]: Docker Documentation - What is a container? https://docs.docker.com/get-started/docker-concepts/the-basics/what-is-a-container/

[^2]: Docker Hub Security Best Practices. https://docs.docker.com/docker-hub/security/
